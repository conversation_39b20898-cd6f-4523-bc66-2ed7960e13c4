<!-- UserDepartment.svelte -->
<script lang="ts">
	import { t } from '$lib/stores/i18n';

	import {
		PlusOutline,
		MinusOutline,
		TrashBinSolid,
		EditOutline,
		CheckOutline
	} from 'flowbite-svelte-icons';
	import { enhance } from '$app/forms';
	import { invalidateAll } from '$app/navigation';
	import { Accordion, AccordionItem, Indicator, Button } from 'flowbite-svelte';
	import { onMount, tick } from 'svelte';

	export let departmentNames = [];
	import { colorOptions, getColorClass } from '$lib/utils'; // adjust the path if needed

	let isAddingDepartment = false;
	let departmentFormErrors: string | null = null;
	let departmentToDelete: number | null = null;
	let departmentToEdit: number | null = null;
	let isSubmittingDepartment = false;

	// Shared state for color picker
	let colorPickerOpen = false;
	let activePickerId: string | null = null;

	// For new department
	let newDepartmentColor = colorOptions[0]?.name || 'gray';

	// For editing
	let selectedColor = colorOptions[0]?.name || 'gray';

	onMount(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (colorPickerOpen && !(event.target as HTMLElement).closest('.color-picker-area')) {
				colorPickerOpen = false;
				activePickerId = null;
			}
		};
		document.addEventListener('click', handleClickOutside);
		return () => document.removeEventListener('click', handleClickOutside);
	});

	async function toggleColorPicker(id: string) {
		if (activePickerId === id && colorPickerOpen) {
			colorPickerOpen = false;
			activePickerId = null;
		} else {
			activePickerId = id;
			await tick();
			colorPickerOpen = true;
		}
	}

	function chooseColor(name: string) {
		if (departmentToEdit !== null) {
			selectedColor = name;
		} else {
			newDepartmentColor = name;
		}
		colorPickerOpen = false;
		activePickerId = null;
	}

	function handleDepartmentSubmit() {
		isSubmittingDepartment = true;
		departmentFormErrors = null;

		return async ({ result, update }) => {
			isSubmittingDepartment = false;

			if (result.type === 'success') {
				const form = document.querySelector('form') as HTMLFormElement;
				form?.reset();
				isAddingDepartment = false;
				departmentToEdit = null;
				await invalidateAll();
			} else if (result.type === 'failure') {
				departmentFormErrors = result.data?.error || 'An unexpected error occurred';
			}
		};
	}

	function confirmDepartmentDelete(deptId: number) {
		departmentToDelete = deptId;
	}

	function cancelDepartmentDelete() {
		departmentToDelete = null;
	}

	function startDepartmentEdit(department) {
		departmentToEdit = department.id;
		selectedColor = department.color || 'gray';
	}

	function cancelDepartmentEdit() {
		departmentToEdit = null;
		departmentFormErrors = null;
		colorPickerOpen = false;
		activePickerId = null;
	}
</script>

<AccordionItem>
	<span slot="header" class="flex w-full flex-col">
		<h2 class="text-xl font-medium text-gray-700">{t('departments')}</h2>
		<p class="text-sm text-gray-500">{t('departments_desc')}</p>
	</span>
	<div class="space-y-4">
		{#if departmentNames.length > 0}
			<ul class="space-y-2">
				{#each departmentNames as department (department.id)}
					<li class="flex items-center justify-between rounded-lg px-4 py-2">
						{#if departmentToEdit === department.id}
							<form
								method="POST"
								action="?/update_department"
								use:enhance={handleDepartmentSubmit}
								class="w-full space-y-4"
							>
								<input type="hidden" name="backend_department_id" value={department.id} />
								<input type="hidden" name="color" value={selectedColor} />
								<div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
									<!-- Added Color Picker for Edit mode -->
									<div class="space-y-2">
										<label class="block text-sm font-medium text-gray-700">
											{t('select_color')}
										</label>
										<div class="relative">
											<button
												type="button"
												class="flex items-center rounded-lg border border-gray-300 px-3 py-2"
												on:click|stopPropagation={() =>
													toggleColorPicker(`edit-dept-${department.id}`)}
											>
												<!-- <Indicator size="lg" color={selectedColor} class="mr-2" /> -->
												<Indicator size="lg" class={`mr-1 ${getColorClass(selectedColor)}`} />
												<!-- <span>{selectedColor}</span> -->
											</button>
											{#if activePickerId === `edit-dept-${department.id}` && colorPickerOpen}
												<div
													class="color-picker-area absolute left-0 top-full z-20 mt-2 rounded-lg bg-white p-3 shadow-lg"
													style="min-width: 170px;"
												>
													<div class="grid grid-cols-6 gap-3">
														{#each colorOptions as opt}
															<button
																type="button"
																class={`h-6 w-6 cursor-pointer rounded-full ${opt.class} border ${selectedColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
																on:click|stopPropagation={() => chooseColor(opt.name)}
																aria-label={`Select ${opt.name} color`}
															></button>
														{/each}
													</div>
												</div>
											{/if}
										</div>
									</div>

									<div class="space-y-2">
										<label for="dept_name" class="block text-sm font-medium text-gray-700">
											{t('name')}
										</label>
										<input
											type="text"
											id="dept_name"
											name="name"
											required
											value={department.name}
											class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
											placeholder="Enter department name..."
										/>
									</div>
									<div class="space-y-2">
										<label for="dept_code" class="block text-sm font-medium text-gray-700">
											{t('code')}
										</label>
										<input
											type="text"
											id="dept_code"
											name="code"
											required
											value={department.code}
											class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
											placeholder="Enter department code..."
										/>
									</div>
								</div>

								<div class="space-y-2">
									<label for="dept_description" class="block text-sm font-medium text-gray-700">
										{t('description')}
									</label>
									<textarea
										id="dept_description"
										name="description"
										required
										class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
										placeholder="Enter department description...">{department.description}</textarea
									>
								</div>
								<input
									type="hidden"
									id="dept_active"
									name="is_active"
									value="true"
									checked={department.is_active}
									class="h-5 w-5"
								/>
								{#if departmentFormErrors}
									<div class="rounded-lg bg-red-100 p-3 text-red-700">
										{departmentFormErrors}
									</div>
								{/if}
								<div class="mt-4 flex space-x-2">
									<Button
										type="submit"
										disabled={isSubmittingDepartment}
										color="green"
										class="disabled:opacity-20"
									>
										<CheckOutline class="mr-2 h-4 w-4" />
										{isSubmittingDepartment ? t('updating') : t('update')}
									</Button>
									<Button type="button" on:click={cancelDepartmentEdit} color="light">
										{t('cancel')}
									</Button>
								</div>
							</form>
						{:else}
							<!-- Department Info Section -->
							<div class="flex-grow space-y-2">
								<div class="flex items-center gap-3">
									<!-- <Indicator size="lg" color={department.color ?? 'gray'} class="mr-2" /> -->
									<Indicator size="lg" class={`${getColorClass(department.color)} mr-2`} />
									<h3 class="text-lg font-medium text-gray-900">
										{department.name} ({department.code})
									</h3>
								</div>

								{#if department.description}
									<div class="mt-2 text-sm text-gray-700">
										<div>
											<span class="mb-1 block text-gray-500">{t('description')}: </span>
											<p class="border-gray-200">{department.description}</p>
										</div>
									</div>
								{/if}
							</div>

							<div class="flex items-center space-x-2">
								{#if departmentToDelete === department.id}
									<div class="flex space-x-2">
										<form
											method="POST"
											action="?/delete_department"
											use:enhance={handleDepartmentSubmit}
											class="flex items-center"
										>
											<input type="hidden" name="backend_department_id" value={department.id} />
											<Button type="submit" disabled={isSubmittingDepartment} color="red">
												<TrashBinSolid class="mr-2 h-4 w-4" />
												{t('delete')}
											</Button>
										</form>
										<Button on:click={cancelDepartmentDelete} color="light">
											{t('cancel')}
										</Button>
									</div>
								{:else}
									<button
										on:click={() => startDepartmentEdit(department)}
										class="mr-2 text-gray-400 hover:text-gray-800"
									>
										<EditOutline class="h-5 w-5" />
									</button>
									<button
										on:click={() => confirmDepartmentDelete(department.id)}
										class="text-red-500 hover:text-red-700"
									>
										<TrashBinSolid class="h-5 w-5" />
									</button>
								{/if}
							</div>
						{/if}
					</li>

					<!-- Divider line between sections -->
					<hr class="my-6 border-t border-gray-300" />
				{/each}
			</ul>
		{:else if !isAddingDepartment}
			<p class="text-center italic text-gray-500">{t('no_departments')}</p>
		{/if}

		<form
			method="POST"
			action="?/create_new_department_action"
			use:enhance={handleDepartmentSubmit}
			class="mt-4 space-y-4"
		>
			<div class="mt-4 flex space-x-2">
				{#if isAddingDepartment}
					{#if departmentFormErrors}
						<div class="rounded-lg bg-red-100 p-3 text-red-700">
							{departmentFormErrors}
						</div>
					{/if}
					<div>
						<div class="flex-col">
							<div class="grid grid-cols-3 gap-4 sm:grid-cols-3">
								<div class="space-y-2">
									<!-- <label class="block text-sm font-medium text-gray-700">
							                {t('select_color')}
						                </label> -->
									<div class="relative">
										<button
											type="button"
											class="w-30px flex items-center rounded-lg px-3 py-2"
											on:click|stopPropagation={() => toggleColorPicker('new-dept')}
										>
											<!-- <Indicator size="lg" color={newDepartmentColor} class="mr-2" /> -->
											<Indicator size="lg" class={`mr-1 ${getColorClass(newDepartmentColor)}`} />
											<!-- <span>{newDepartmentColor}</span> -->
										</button>
										<input type="hidden" name="color" value={newDepartmentColor} />
										{#if activePickerId === 'new-dept' && colorPickerOpen}
											<div
												class="color-picker-area absolute left-0 top-full z-20 mt-2 rounded-lg bg-white p-3 shadow-lg"
												style="min-width: 170px;"
											>
												<div class="grid grid-cols-6 gap-3">
													{#each colorOptions as opt}
														<button
															type="button"
															class={`h-6 w-6 cursor-pointer rounded-full ${opt.class} border ${newDepartmentColor === opt.name ? 'ring-2 ring-gray-400' : 'border-transparent'}`}
															on:click|stopPropagation={() => chooseColor(opt.name)}
															aria-label={`Select ${opt.name} color`}
														></button>
													{/each}
												</div>
											</div>
										{/if}
									</div>
								</div>

								<div class="space-y-2">
									<!-- <label for="new_dept_name" class="block text-sm font-medium text-gray-700">
                                        {t('name')}
                                    </label> -->
									<input
										type="text"
										id="new_dept_name"
										name="name"
										required
										class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
										placeholder={t('name_desc')}
									/>
								</div>

								<div class="space-y-2">
									<!-- <label for="new_dept_code" class="block text-sm font-medium text-gray-700">
                                        {t('code')}
                                    </label> -->
									<input
										type="text"
										id="new_dept_code"
										name="code"
										required
										class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
										placeholder={t('code_desc')}
									/>
								</div>
							</div>
							<div class="flex w-full">
								<div class="space-y-2">
									<!-- <label for="new_dept_description" class="block text-sm font-medium text-gray-700">
                                        {t('description')}
                                    </label> -->
									<textarea
										id="new_dept_description"
										name="description"
										required
										class="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
										placeholder={t('description_desc')}
									></textarea>
								</div>
								<input
									type="hidden"
									id="new_dept_active"
									name="is_active"
									value="true"
									checked
									class="h-5 w-5"
								/>
							</div>
						</div>
						<div class="flex">
							<div class="flex space-x-2">
								<Button
									type="submit"
									disabled={isSubmittingDepartment}
									color="green"
									class="w-full disabled:opacity-20"
								>
									<CheckOutline class="mr-2 h-4 w-4" />
									{isSubmittingDepartment ? t('creating') : t('confirm')}
								</Button>
								<Button
									type="button"
									color="light"
									on:click={() => {
										isAddingDepartment = !isAddingDepartment;
										departmentFormErrors = null;
									}}
								>
									{t('cancel')}
								</Button>
							</div>
						</div>
					</div>
				{/if}
				{#if isAddingDepartment}
					<!-- <div class="flex space-x-2">
						<Button
							type="submit"
							disabled={isSubmittingDepartment}
							color="green"
							class="w-full disabled:opacity-20"
						>
							<CheckOutline class="mr-2 h-4 w-4" />
							{isSubmittingDepartment ? t('creating') : t('confirm')}
						</Button>
						<Button
							type="button"
							color="light"
							on:click={() => {
								isAddingDepartment = !isAddingDepartment;
								departmentFormErrors = null;
							}}
						>
							{t('cancel')}
						</Button>
					</div> -->
				{:else}
					<Button
						type="button"
						color="blue"
						on:click={() => {
							isAddingDepartment = !isAddingDepartment;
							departmentFormErrors = null;
						}}
					>
						<PlusOutline class="mr-1 inline-block h-5 w-5" />
						<span>{t('add_department')}</span>
					</Button>
				{/if}
			</div>
		</form>
	</div>
</AccordionItem>
